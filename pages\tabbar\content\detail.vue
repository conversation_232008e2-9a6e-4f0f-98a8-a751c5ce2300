<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="back-btn" @tap="goBack">
					<tui-icon name="arrowleft" size="24" color="#333"></tui-icon>
				</view>
				<view class="navbar-title">瓦片购买</view>
				<view class="placeholder"></view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper" v-if="contentDetail">
			<!-- 标题和发布信息 -->
			<view class="detail-header">
				<view class="detail-title">{{ contentDetail.title }}</view>
				<view class="publish-info">
					<view class="publisher-info">
						<text class="publisher-label">发布人名称</text>
						<text class="publisher-name">{{ contentDetail.publisher || '系统管理员' }}</text>
					</view>
					<view class="publish-time">
						<text class="time-label">发布时间</text>
						<text class="time-value">{{ formatTime(contentDetail.createTime) }}</text>
					</view>
				</view>
			</view>

			<!-- 内容详情 -->
			<view class="detail-content">
				<rich-text :nodes="contentDetail.pubContent"></rich-text>
			</view>

			<!-- 图片展示 -->
			<view class="image-gallery" v-if="contentDetail.fileListInfo && contentDetail.fileListInfo.length > 0">
				<view class="image-grid">
					<view
						class="image-item"
						v-for="(image, index) in contentDetail.fileListInfo"
						:key="image.id"
						@tap="previewImage(index)"
					>
						<image
							:src="image.url"
							mode="aspectFill"
							class="gallery-image"
							:lazy-load="true"
						/>
						<text class="image-label">图{{ index + 1 }}</text>
					</view>
				</view>
			</view>

			<!-- 浏览量 -->
			<view class="view-count">
				<text class="count-text">阅读量：186</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-wrapper" v-if="loading">
			<tui-loadmore></tui-loadmore>
		</view>

		<!-- 错误状态 -->
		<view class="error-wrapper" v-if="error && !loading">
			<text class="error-text">{{ error }}</text>
			<tui-button @click="loadDetail" size="28" width="200rpx" height="60rpx">重试</tui-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			pubId: '',
			contentDetail: null,
			loading: true,
			error: '',
			statusBarHeight: 0
		}
	},
	onLoad(options) {
		this.pubId = options.pubId;
		this.getSystemInfo();
		if (this.pubId) {
			this.loadDetail();
		} else {
			this.error = '参数错误';
			this.loading = false;
		}
	},
	methods: {
		// 获取系统信息
		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;
		},

		// 加载详情数据
		async loadDetail() {
			this.loading = true;
			this.error = '';

			try {
				const params = {
					pubId: this.pubId
				};

				const response = await this.tui.request('/api/admin/biz-pub-content/getDetailById', 'POST', params);

				if (response.code === '0000') {
					this.contentDetail = response.data;
				} else {
					this.error = response.message || '加载失败';
				}
			} catch (error) {
				console.error('加载详情失败:', error);
				this.error = '网络错误，请稍后重试';
			} finally {
				this.loading = false;
			}
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}/${month}/${day}`;
		},

		// 预览图片
		previewImage(index) {
			if (!this.contentDetail.fileListInfo || this.contentDetail.fileListInfo.length === 0) return;

			const urls = this.contentDetail.fileListInfo.map(item => item.url);
			uni.previewImage({
				current: index,
				urls: urls
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.custom-navbar {
	background-color: #fff;
	border-bottom: 1px solid #eee;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
}

.back-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.placeholder {
	width: 60rpx;
}

.content-wrapper {
	margin-top: 88rpx;
	padding: 30rpx;
}

.detail-header {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.detail-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	margin-bottom: 30rpx;
}

.publish-info {
	border-top: 1px solid #eee;
	padding-top: 20rpx;
}

.publisher-info,
.publish-time {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.publisher-label,
.time-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 20rpx;
	min-width: 120rpx;
}

.publisher-name,
.time-value {
	font-size: 28rpx;
	color: #333;
}

.detail-content {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	line-height: 1.6;
}

.image-gallery {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.image-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.image-item {
	width: calc(50% - 10rpx);
	position: relative;
}

.gallery-image {
	width: 100%;
	height: 200rpx;
	border-radius: 8rpx;
}

.image-label {
	position: absolute;
	bottom: 10rpx;
	left: 10rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
}

.view-count {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx 30rpx;
	text-align: center;
}

.count-text {
	font-size: 28rpx;
	color: #666;
}

.loading-wrapper,
.error-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	gap: 30rpx;
}

.error-text {
	font-size: 28rpx;
	color: #999;
}
</style>